import CoreML
import Combine
import Foundation

@MainActor
class MLProcessingManager: ObservableObject {
    static let shared = MLProcessingManager()

    // MARK: - Published Properties
    @Published var isProcessing = false
    @Published var error: MotionFitProError?
    @Published var currentExercise: ExerciseType?
    @Published var exerciseConfidence: Float = 0.0
    @Published var repCount: Int = 0
    @Published var formScore: Float = 0.0
    @Published var lastAnalysis: ExerciseAnalysis?

    // MARK: - Private Properties
    private let exerciseClassifier = ExerciseClassifier()
    private let repCountingEngine = RepCountingEngine()
    private let movementDetector = MovementDetector()
    private let formAnalyzer = FormAnalyzer()
    private let optimizationManager = MLOptimizationManager.shared

    private var cancellables = Set<AnyCancellable>()
    private let logger = Logger.shared
    private var poseBuffer: [BodyPoseData] = []
    private let maxBufferSize = 60 // 1 second at 60fps

    // MARK: - Initialization
    private init() {
        setupSubscriptions()
        configureOptimization()
    }

    // MARK: - Public Interface

    func startProcessing() {
        isProcessing = true
        logger.info("ML processing started", category: .mlProcessing)
    }

    func stopProcessing() {
        isProcessing = false
        clearBuffer()
        resetCounters()
        logger.info("ML processing stopped", category: .mlProcessing)
    }

    /// Process new pose data through the ML pipeline
    func processPoseData(_ poseData: BodyPoseData) async {
        guard isProcessing else { return }

        // Add to buffer
        addToPoseBuffer(poseData)

        // Process through movement detector
        movementDetector.processMovement(poseData)

        // Process through exercise classifier
        exerciseClassifier.process(pose: poseData)

        // Process through rep counting if exercise is detected
        if let exercise = currentExercise {
            repCountingEngine.add(poseData)

            // Analyze form
            let analysis = await analyzeExerciseForm(poseData, exerciseType: exercise)
            await MainActor.run {
                self.lastAnalysis = analysis
                self.formScore = analysis.formScore
            }
        }
    }

    /// Analyze exercise form for a specific exercise type
    func analyzeExercise(_ poseData: BodyPoseData, exerciseType: ExerciseType) async -> ExerciseAnalysis {
        return await analyzeExerciseForm(poseData, exerciseType: exerciseType)
    }

    /// Set the target exercise for focused analysis
    func setTargetExercise(_ exercise: ExerciseType) {
        currentExercise = exercise
        repCountingEngine.setActiveExercise(exercise)
        resetCounters()
        logger.info("Target exercise set to: \(exercise)", category: .mlProcessing)
    }

    /// Reset all counters and analysis state
    func resetAnalysis() {
        resetCounters()
        clearBuffer()
        currentExercise = nil
        exerciseConfidence = 0.0
        formScore = 0.0
        lastAnalysis = nil
        logger.info("ML analysis reset", category: .mlProcessing)
    }

    // MARK: - Private Methods

    private func setupSubscriptions() {
        // Exercise classification results
        exerciseClassifier.classificationPublisher
            .receive(on: DispatchQueue.main)
            .sink { [weak self] result in
                self?.handleClassificationResult(result)
            }
            .store(in: &cancellables)

        // Rep counting results
        repCountingEngine.$repCount
            .receive(on: DispatchQueue.main)
            .assign(to: &$repCount)

        // Movement detection results
        movementDetector.$currentExercise
            .receive(on: DispatchQueue.main)
            .sink { [weak self] exercise in
                if let exercise = exercise, self?.currentExercise != exercise {
                    self?.setTargetExercise(exercise)
                }
            }
            .store(in: &cancellables)

        movementDetector.$movementQuality
            .receive(on: DispatchQueue.main)
            .sink { [weak self] quality in
                self?.formScore = quality
            }
            .store(in: &cancellables)
    }

    private func configureOptimization() {
        Task {
            await optimizationManager.optimizeForRealTimeInference()
        }
    }

    private func handleClassificationResult(_ result: ExerciseClassifier.ClassificationResult) {
        exerciseConfidence = Float(result.confidence)

        if result.confidence > 0.8 && result.exercise != .unknown {
            if currentExercise != result.exercise {
                setTargetExercise(result.exercise)
            }
        }
    }

    private func addToPoseBuffer(_ poseData: BodyPoseData) {
        poseBuffer.append(poseData)
        if poseBuffer.count > maxBufferSize {
            poseBuffer.removeFirst()
        }
    }

    private func clearBuffer() {
        poseBuffer.removeAll()
    }

    private func resetCounters() {
        repCount = 0
        formScore = 0.0
        exerciseConfidence = 0.0
    }

    private func analyzeExerciseForm(_ poseData: BodyPoseData, exerciseType: ExerciseType) async -> ExerciseAnalysis {
        let startTime = CFAbsoluteTimeGetCurrent()

        // Analyze form based on exercise type
        let formAnalysis = await formAnalyzer.analyzeForm(poseData, for: exerciseType)

        // Calculate overall form score
        let formScore = calculateFormScore(formAnalysis)

        // Generate feedback
        let feedback = generateFormFeedback(formAnalysis, exerciseType: exerciseType)

        // Create analysis result
        let analysis = ExerciseAnalysis(
            exerciseType: exerciseType,
            poseData: poseData,
            formScore: formScore,
            formAnalysis: formAnalysis,
            feedback: feedback,
            timestamp: Date(),
            processingTime: CFAbsoluteTimeGetCurrent() - startTime
        )

        logger.debug("Form analysis completed for \(exerciseType) with score: \(formScore)", category: .mlProcessing)

        return analysis
    }

    private func calculateFormScore(_ formAnalysis: FormAnalysis) -> Float {
        let weights: [FormCriteria: Float] = [
            .jointAlignment: 0.3,
            .rangeOfMotion: 0.25,
            .stability: 0.2,
            .timing: 0.15,
            .symmetry: 0.1
        ]

        var totalScore: Float = 0.0
        var totalWeight: Float = 0.0

        for (criteria, weight) in weights {
            if let score = formAnalysis.scores[criteria] {
                totalScore += score * weight
                totalWeight += weight
            }
        }

        return totalWeight > 0 ? totalScore / totalWeight : 0.0
    }

    private func generateFormFeedback(_ formAnalysis: FormAnalysis, exerciseType: ExerciseType) -> [FormFeedback] {
        var feedback: [FormFeedback] = []

        // Check each form criteria and generate specific feedback
        for (criteria, score) in formAnalysis.scores {
            if score < 0.7 { // Below acceptable threshold
                let feedbackMessage = generateFeedbackMessage(for: criteria, exerciseType: exerciseType, score: score)
                let feedbackItem = FormFeedback(
                    type: .correction,
                    message: feedbackMessage,
                    exerciseType: exerciseType,
                    severity: score < 0.5 ? .high : .medium,
                    correctionSuggestion: generateCorrectionSuggestion(for: criteria, exerciseType: exerciseType)
                )
                feedback.append(feedbackItem)
            }
        }

        // Add positive feedback for good form
        if formAnalysis.overallScore > 0.8 {
            let encouragementFeedback = FormFeedback(
                type: .encouragement,
                message: "Excellent form! Keep it up!",
                exerciseType: exerciseType,
                severity: .low
            )
            feedback.append(encouragementFeedback)
        }

        return feedback
    }

    private func generateFeedbackMessage(for criteria: FormCriteria, exerciseType: ExerciseType, score: Float) -> String {
        switch (criteria, exerciseType) {
        case (.jointAlignment, .squat):
            return "Keep your knees aligned with your toes"
        case (.jointAlignment, .pushUp):
            return "Maintain straight line from head to heels"
        case (.rangeOfMotion, .squat):
            return "Go deeper - aim for thighs parallel to ground"
        case (.rangeOfMotion, .pushUp):
            return "Lower your chest closer to the ground"
        case (.stability, _):
            return "Focus on maintaining balance and control"
        case (.timing, _):
            return "Slow down the movement for better control"
        case (.symmetry, _):
            return "Keep both sides of your body moving equally"
        default:
            return "Focus on proper form"
        }
    }

    private func generateCorrectionSuggestion(for criteria: FormCriteria, exerciseType: ExerciseType) -> String? {
        switch (criteria, exerciseType) {
        case (.jointAlignment, .squat):
            return "Point your toes slightly outward and track your knees over them"
        case (.jointAlignment, .pushUp):
            return "Engage your core and keep your body in a straight line"
        case (.rangeOfMotion, .squat):
            return "Sit back into the squat as if sitting in a chair"
        case (.rangeOfMotion, .pushUp):
            return "Lower until your chest nearly touches the ground"
        case (.stability, _):
            return "Slow down and focus on controlled movements"
        case (.timing, _):
            return "Take 2-3 seconds for each phase of the movement"
        case (.symmetry, _):
            return "Check that both sides of your body are moving equally"
        default:
            return nil
        }
    }
}

// MARK: - Supporting Types

struct ExerciseAnalysis {
    let exerciseType: ExerciseType
    let poseData: BodyPoseData
    let formScore: Float
    let formAnalysis: FormAnalysis
    let feedback: [FormFeedback]
    let timestamp: Date
    let processingTime: TimeInterval
}

struct FormAnalysis {
    let scores: [FormCriteria: Float]
    let overallScore: Float
    let criticalIssues: [FormIssue]
    let recommendations: [String]
}

enum FormCriteria: String, CaseIterable {
    case jointAlignment = "joint_alignment"
    case rangeOfMotion = "range_of_motion"
    case stability = "stability"
    case timing = "timing"
    case symmetry = "symmetry"

    var displayName: String {
        switch self {
        case .jointAlignment: return "Joint Alignment"
        case .rangeOfMotion: return "Range of Motion"
        case .stability: return "Stability"
        case .timing: return "Timing"
        case .symmetry: return "Symmetry"
        }
    }
}

struct FormIssue {
    let criteria: FormCriteria
    let severity: FeedbackSeverity
    let description: String
    let jointInvolved: JointName?
}

// MARK: - Logger Category Extension
extension Logger.Category {
    static let mlProcessing = Logger.Category(rawValue: "mlProcessing")
}