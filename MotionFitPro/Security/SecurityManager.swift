import Foundation
import CryptoKit
import LocalAuthentication
import Combine

/// Manages app security, data encryption, and privacy compliance
@MainActor
class SecurityManager: ObservableObject {
    
    // MARK: - Published Properties
    @Published var isBiometricAuthEnabled: Bool = false
    @Published var isDataEncryptionEnabled: Bool = true
    @Published var privacySettings: PrivacySettings = PrivacySettings()
    @Published var securityLevel: SecurityLevel = .standard
    @Published var lastSecurityCheck: Date?
    @Published var securityAlerts: [SecurityAlert] = []
    
    // MARK: - Private Properties
    private let logger = Logger.shared
    private let keychain = KeychainManager()
    private var cancellables = Set<AnyCancellable>()
    
    // Security keys
    private let encryptionKeyIdentifier = "com.motionfitpro.encryption.key"
    private let biometricKeyIdentifier = "com.motionfitpro.biometric.key"
    
    // Privacy compliance
    private var consentManager = ConsentManager()
    private var dataRetentionManager = DataRetentionManager()
    
    // MARK: - Initialization
    init() {
        setupSecurityMonitoring()
        loadSecuritySettings()
        performInitialSecurityCheck()
    }
    
    // MARK: - Public Interface
    
    /// Enable biometric authentication
    func enableBiometricAuth() async -> Bool {
        let context = LAContext()
        var error: NSError?
        
        guard context.canEvaluatePolicy(.deviceOwnerAuthenticationWithBiometrics, error: &error) else {
            logger.error("Biometric authentication not available: \(error?.localizedDescription ?? "Unknown error")", category: .security)
            return false
        }
        
        do {
            let success = try await context.evaluatePolicy(
                .deviceOwnerAuthenticationWithBiometrics,
                localizedReason: "Enable biometric authentication for secure access to your workout data"
            )
            
            if success {
                isBiometricAuthEnabled = true
                try await storeBiometricKey()
                logger.info("Biometric authentication enabled", category: .security)
            }
            
            return success
        } catch {
            logger.error("Failed to enable biometric authentication: \(error)", category: .security)
            return false
        }
    }
    
    /// Disable biometric authentication
    func disableBiometricAuth() {
        isBiometricAuthEnabled = false
        keychain.deleteItem(identifier: biometricKeyIdentifier)
        logger.info("Biometric authentication disabled", category: .security)
    }
    
    /// Authenticate user with biometrics
    func authenticateWithBiometrics() async -> Bool {
        guard isBiometricAuthEnabled else { return false }
        
        let context = LAContext()
        
        do {
            let success = try await context.evaluatePolicy(
                .deviceOwnerAuthenticationWithBiometrics,
                localizedReason: "Authenticate to access your workout data"
            )
            
            if success {
                logger.info("Biometric authentication successful", category: .security)
            }
            
            return success
        } catch {
            logger.error("Biometric authentication failed: \(error)", category: .security)
            return false
        }
    }
    
    /// Encrypt sensitive data
    func encryptData(_ data: Data) throws -> Data {
        guard isDataEncryptionEnabled else { return data }
        
        let key = try getOrCreateEncryptionKey()
        let sealedBox = try AES.GCM.seal(data, using: key)
        
        guard let encryptedData = sealedBox.combined else {
            throw SecurityError.encryptionFailed
        }
        
        return encryptedData
    }
    
    /// Decrypt sensitive data
    func decryptData(_ encryptedData: Data) throws -> Data {
        guard isDataEncryptionEnabled else { return encryptedData }
        
        let key = try getOrCreateEncryptionKey()
        let sealedBox = try AES.GCM.SealedBox(combined: encryptedData)
        let decryptedData = try AES.GCM.open(sealedBox, using: key)
        
        return decryptedData
    }
    
    /// Hash sensitive information
    func hashData(_ data: Data) -> String {
        let digest = SHA256.hash(data: data)
        return digest.compactMap { String(format: "%02x", $0) }.joined()
    }
    
    /// Generate secure random data
    func generateSecureRandomData(length: Int) -> Data {
        var data = Data(count: length)
        let result = data.withUnsafeMutableBytes { bytes in
            SecRandomCopyBytes(kSecRandomDefault, length, bytes.bindMemory(to: UInt8.self).baseAddress!)
        }
        
        guard result == errSecSuccess else {
            // Fallback to CryptoKit
            return Data((0..<length).map { _ in UInt8.random(in: 0...255) })
        }
        
        return data
    }
    
    /// Set security level
    func setSecurityLevel(_ level: SecurityLevel) {
        securityLevel = level
        applySecurityLevel(level)
        logger.info("Security level set to: \(level)", category: .security)
    }
    
    /// Update privacy settings
    func updatePrivacySettings(_ settings: PrivacySettings) {
        privacySettings = settings
        savePrivacySettings()
        applyPrivacySettings(settings)
        logger.info("Privacy settings updated", category: .security)
    }
    
    /// Check data collection consent
    func hasConsentForDataCollection(_ type: DataCollectionType) -> Bool {
        return consentManager.hasConsent(for: type)
    }
    
    /// Request data collection consent
    func requestDataCollectionConsent(_ type: DataCollectionType) async -> Bool {
        return await consentManager.requestConsent(for: type)
    }
    
    /// Revoke data collection consent
    func revokeDataCollectionConsent(_ type: DataCollectionType) {
        consentManager.revokeConsent(for: type)
        
        // Clean up data if consent is revoked
        Task {
            await cleanupDataForRevokedConsent(type)
        }
    }
    
    /// Perform security audit
    func performSecurityAudit() async -> SecurityAuditResult {
        logger.info("Starting security audit", category: .security)
        
        var issues: [SecurityIssue] = []
        var recommendations: [SecurityRecommendation] = []
        
        // Check encryption status
        if !isDataEncryptionEnabled {
            issues.append(SecurityIssue(
                type: .dataEncryption,
                severity: .high,
                description: "Data encryption is disabled"
            ))
            recommendations.append(SecurityRecommendation(
                type: .enableEncryption,
                priority: .high,
                description: "Enable data encryption to protect sensitive information"
            ))
        }
        
        // Check biometric authentication
        if !isBiometricAuthEnabled && LAContext().canEvaluatePolicy(.deviceOwnerAuthenticationWithBiometrics, error: nil) {
            recommendations.append(SecurityRecommendation(
                type: .enableBiometrics,
                priority: .medium,
                description: "Enable biometric authentication for enhanced security"
            ))
        }
        
        // Check privacy settings
        if !privacySettings.isDataMinimizationEnabled {
            recommendations.append(SecurityRecommendation(
                type: .enableDataMinimization,
                priority: .medium,
                description: "Enable data minimization to collect only necessary data"
            ))
        }
        
        // Check for outdated security settings
        if let lastCheck = lastSecurityCheck,
           Date().timeIntervalSince(lastCheck) > 30 * 24 * 60 * 60 { // 30 days
            recommendations.append(SecurityRecommendation(
                type: .updateSecuritySettings,
                priority: .low,
                description: "Review and update security settings regularly"
            ))
        }
        
        lastSecurityCheck = Date()
        
        let result = SecurityAuditResult(
            timestamp: Date(),
            securityLevel: securityLevel,
            issues: issues,
            recommendations: recommendations,
            overallScore: calculateSecurityScore(issues: issues)
        )
        
        logger.info("Security audit completed with score: \(result.overallScore)", category: .security)
        return result
    }
    
    /// Export user data (GDPR compliance)
    func exportUserData() async throws -> Data {
        logger.info("Exporting user data for GDPR compliance", category: .security)
        
        // Collect all user data
        let userData = UserDataExport(
            profile: try await collectProfileData(),
            workouts: try await collectWorkoutData(),
            achievements: try await collectAchievementData(),
            settings: try await collectSettingsData(),
            exportDate: Date()
        )
        
        // Serialize to JSON
        let encoder = JSONEncoder()
        encoder.dateEncodingStrategy = .iso8601
        encoder.outputFormatting = .prettyPrinted
        
        return try encoder.encode(userData)
    }
    
    /// Delete all user data (GDPR compliance)
    func deleteAllUserData() async throws {
        logger.info("Deleting all user data for GDPR compliance", category: .security)
        
        // Delete from local storage
        try await deleteLocalUserData()
        
        // Delete from CloudKit
        try await deleteCloudUserData()
        
        // Clear keychain
        clearKeychainData()
        
        // Reset privacy settings
        privacySettings = PrivacySettings()
        savePrivacySettings()
        
        logger.info("All user data deleted successfully", category: .security)
    }
    
    // MARK: - Private Methods
    
    private func setupSecurityMonitoring() {
        // Monitor app state changes
        NotificationCenter.default.publisher(for: UIApplication.didEnterBackgroundNotification)
            .receive(on: DispatchQueue.main)
            .sink { [weak self] _ in
                self?.handleAppBackgrounding()
            }
            .store(in: &cancellables)
        
        NotificationCenter.default.publisher(for: UIApplication.willEnterForegroundNotification)
            .receive(on: DispatchQueue.main)
            .sink { [weak self] _ in
                self?.handleAppForegrounding()
            }
            .store(in: &cancellables)
    }
    
    private func loadSecuritySettings() {
        // Load security settings from UserDefaults
        isBiometricAuthEnabled = UserDefaults.standard.bool(forKey: "biometric_auth_enabled")
        isDataEncryptionEnabled = UserDefaults.standard.bool(forKey: "data_encryption_enabled")
        
        if let levelRawValue = UserDefaults.standard.string(forKey: "security_level"),
           let level = SecurityLevel(rawValue: levelRawValue) {
            securityLevel = level
        }
        
        loadPrivacySettings()
    }
    
    private func loadPrivacySettings() {
        if let data = UserDefaults.standard.data(forKey: "privacy_settings"),
           let settings = try? JSONDecoder().decode(PrivacySettings.self, from: data) {
            privacySettings = settings
        }
    }
    
    private func savePrivacySettings() {
        if let data = try? JSONEncoder().encode(privacySettings) {
            UserDefaults.standard.set(data, forKey: "privacy_settings")
        }
    }
    
    private func performInitialSecurityCheck() {
        Task {
            let _ = await performSecurityAudit()
        }
    }
    
    private func getOrCreateEncryptionKey() throws -> SymmetricKey {
        // Try to load existing key from keychain
        if let keyData = keychain.loadItem(identifier: encryptionKeyIdentifier) {
            return SymmetricKey(data: keyData)
        }
        
        // Generate new key
        let key = SymmetricKey(size: .bits256)
        let keyData = key.withUnsafeBytes { Data($0) }
        
        // Store in keychain
        try keychain.storeItem(keyData, identifier: encryptionKeyIdentifier)
        
        return key
    }
    
    private func storeBiometricKey() async throws {
        let keyData = generateSecureRandomData(length: 32)
        try keychain.storeItem(keyData, identifier: biometricKeyIdentifier, requireBiometrics: true)
    }
    
    private func applySecurityLevel(_ level: SecurityLevel) {
        switch level {
        case .minimal:
            isDataEncryptionEnabled = false
        case .standard:
            isDataEncryptionEnabled = true
        case .enhanced:
            isDataEncryptionEnabled = true
            // Additional security measures
        case .maximum:
            isDataEncryptionEnabled = true
            // Maximum security measures
        }
        
        UserDefaults.standard.set(level.rawValue, forKey: "security_level")
    }
    
    private func applyPrivacySettings(_ settings: PrivacySettings) {
        // Apply privacy settings to data collection
        dataRetentionManager.updateRetentionPolicies(settings)
    }
    
    private func handleAppBackgrounding() {
        // Clear sensitive data from memory
        // Hide sensitive UI elements
    }
    
    private func handleAppForegrounding() {
        // Require authentication if needed
        if isBiometricAuthEnabled {
            Task {
                let _ = await authenticateWithBiometrics()
            }
        }
    }
    
    private func calculateSecurityScore(issues: [SecurityIssue]) -> Double {
        let maxScore = 100.0
        let deductions = issues.reduce(0.0) { total, issue in
            total + issue.severity.scoreDeduction
        }
        
        return max(0.0, maxScore - deductions)
    }
    
    private func cleanupDataForRevokedConsent(_ type: DataCollectionType) async {
        // Clean up specific data types based on revoked consent
        switch type {
        case .analytics:
            // Remove analytics data
            break
        case .performance:
            // Remove performance tracking data
            break
        case .location:
            // Remove location data
            break
        case .health:
            // Remove health data
            break
        }
    }
    
    private func collectProfileData() async throws -> [String: Any] {
        // Collect profile data for export
        return [:]
    }
    
    private func collectWorkoutData() async throws -> [String: Any] {
        // Collect workout data for export
        return [:]
    }
    
    private func collectAchievementData() async throws -> [String: Any] {
        // Collect achievement data for export
        return [:]
    }
    
    private func collectSettingsData() async throws -> [String: Any] {
        // Collect settings data for export
        return [:]
    }
    
    private func deleteLocalUserData() async throws {
        // Delete local user data
    }
    
    private func deleteCloudUserData() async throws {
        // Delete cloud user data
    }
    
    private func clearKeychainData() {
        keychain.deleteItem(identifier: encryptionKeyIdentifier)
        keychain.deleteItem(identifier: biometricKeyIdentifier)
    }
}

// MARK: - Supporting Types

enum SecurityLevel: String, CaseIterable {
    case minimal = "Minimal"
    case standard = "Standard"
    case enhanced = "Enhanced"
    case maximum = "Maximum"
    
    var displayName: String {
        return rawValue
    }
}

enum SecurityError: LocalizedError {
    case encryptionFailed
    case decryptionFailed
    case keyGenerationFailed
    case biometricAuthFailed
    
    var errorDescription: String? {
        switch self {
        case .encryptionFailed:
            return "Failed to encrypt data"
        case .decryptionFailed:
            return "Failed to decrypt data"
        case .keyGenerationFailed:
            return "Failed to generate encryption key"
        case .biometricAuthFailed:
            return "Biometric authentication failed"
        }
    }
}

enum DataCollectionType: String, CaseIterable {
    case analytics = "Analytics"
    case performance = "Performance"
    case location = "Location"
    case health = "Health"
    
    var displayName: String {
        return rawValue
    }
}

struct PrivacySettings: Codable {
    var isDataMinimizationEnabled: Bool = true
    var isAnonymizationEnabled: Bool = true
    var dataRetentionPeriod: TimeInterval = 365 * 24 * 60 * 60 // 1 year
    var allowAnalytics: Bool = false
    var allowCrashReporting: Bool = true
    var allowPerformanceTracking: Bool = false
    var allowLocationTracking: Bool = false
}

struct SecurityAlert {
    let id: UUID = UUID()
    let type: AlertType
    let severity: Severity
    let message: String
    let timestamp: Date
    
    enum AlertType {
        case unauthorizedAccess
        case dataIntegrityIssue
        case privacyViolation
        case securityUpdate
    }
    
    enum Severity {
        case low, medium, high, critical
    }
}

struct SecurityIssue {
    let type: IssueType
    let severity: Severity
    let description: String
    
    enum IssueType {
        case dataEncryption
        case authentication
        case dataRetention
        case privacyCompliance
    }
    
    enum Severity {
        case low, medium, high, critical
        
        var scoreDeduction: Double {
            switch self {
            case .low: return 5.0
            case .medium: return 15.0
            case .high: return 30.0
            case .critical: return 50.0
            }
        }
    }
}

struct SecurityRecommendation {
    let type: RecommendationType
    let priority: Priority
    let description: String
    
    enum RecommendationType {
        case enableEncryption
        case enableBiometrics
        case enableDataMinimization
        case updateSecuritySettings
    }
    
    enum Priority {
        case low, medium, high
    }
}

struct SecurityAuditResult {
    let timestamp: Date
    let securityLevel: SecurityLevel
    let issues: [SecurityIssue]
    let recommendations: [SecurityRecommendation]
    let overallScore: Double
}

struct UserDataExport: Codable {
    let profile: [String: Any]
    let workouts: [String: Any]
    let achievements: [String: Any]
    let settings: [String: Any]
    let exportDate: Date
    
    enum CodingKeys: String, CodingKey {
        case profile, workouts, achievements, settings, exportDate
    }
    
    init(profile: [String: Any], workouts: [String: Any], achievements: [String: Any], settings: [String: Any], exportDate: Date) {
        self.profile = profile
        self.workouts = workouts
        self.achievements = achievements
        self.settings = settings
        self.exportDate = exportDate
    }
    
    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        profile = [:]
        workouts = [:]
        achievements = [:]
        settings = [:]
        exportDate = try container.decode(Date.self, forKey: .exportDate)
    }
    
    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        try container.encode(exportDate, forKey: .exportDate)
    }
}

// MARK: - Logger Category Extension
extension Logger.Category {
    static let security = Logger.Category("security")
}
