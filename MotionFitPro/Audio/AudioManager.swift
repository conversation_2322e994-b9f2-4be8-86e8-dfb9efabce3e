import AVFoundation
import Combine

@MainActor
class AudioManager: ObservableObject {
    static let shared = AudioManager()

    // MARK: - Published Properties
    @Published var isSpeechEnabled = true
    @Published var isSoundEffectsEnabled = true
    @Published var speechVolume: Float = 0.8
    @Published var soundEffectsVolume: Float = 0.6
    @Published var currentCoachingPersonality: CoachingPersonality = .encouraging

    // MARK: - Private Properties
    private let speechSynthesizer = AVSpeechSynthesizer()
    private var audioEngine: AVAudioEngine?
    private var audioSession: AVAudioSession
    private var soundEffectPlayers: [String: AVAudioPlayer] = [:]
    private let logger = Logger.shared

    // Speech queue management
    private var speechQueue: [SpeechItem] = []
    private var isCurrentlySpeaking = false

    // MARK: - Initialization
    private init() {
        audioSession = AVAudioSession.sharedInstance()
        setupAudioSession()
        setupSpeechSynthesizer()
        loadUserPreferences()
    }

    // MARK: - Setup
    private func setupAudioSession() {
        do {
            try audioSession.setCategory(.playback, mode: .default, options: [.mixWithOthers])
            try audioSession.setActive(true)
            logger.info("Audio session configured", category: .audio)
        } catch {
            logger.error("Failed to setup audio session: \(error)", category: .audio)
        }
    }

    private func setupSpeechSynthesizer() {
        speechSynthesizer.delegate = self
    }

    private func loadUserPreferences() {
        isSpeechEnabled = UserDefaults.standard.bool(forKey: "speech_enabled")
        isSoundEffectsEnabled = UserDefaults.standard.bool(forKey: "sound_effects_enabled")
        speechVolume = UserDefaults.standard.float(forKey: "speech_volume")
        soundEffectsVolume = UserDefaults.standard.float(forKey: "sound_effects_volume")

        if speechVolume == 0 { speechVolume = 0.8 }
        if soundEffectsVolume == 0 { soundEffectsVolume = 0.6 }

        if let personalityRaw = UserDefaults.standard.string(forKey: "coaching_personality"),
           let personality = CoachingPersonality(rawValue: personalityRaw) {
            currentCoachingPersonality = personality
        }
    }

    // MARK: - Public Interface

    /// Speak text with coaching personality
    func speak(_ text: String, priority: SpeechPriority = .normal, interrupt: Bool = false) {
        guard isSpeechEnabled else { return }

        let personalizedText = personalizeText(text, for: currentCoachingPersonality)
        let speechItem = SpeechItem(text: personalizedText, priority: priority)

        if interrupt {
            clearSpeechQueue()
            speechSynthesizer.stopSpeaking(at: .immediate)
        }

        addToSpeechQueue(speechItem)
        processNextSpeechItem()
    }

    /// Speak workout-specific feedback
    func speakWorkoutFeedback(_ feedback: WorkoutFeedback) {
        let text = generateFeedbackText(for: feedback)
        speak(text, priority: feedback.priority, interrupt: feedback.shouldInterrupt)
    }

    /// Play sound effect
    func playSoundEffect(_ effect: SoundEffect) {
        guard isSoundEffectsEnabled else { return }

        Task {
            await playSound(effect.filename, volume: soundEffectsVolume)
        }
    }

    /// Stop all audio
    func stopAllAudio() {
        speechSynthesizer.stopSpeaking(at: .immediate)
        clearSpeechQueue()
        stopAllSoundEffects()
    }

    /// Pause speech (for incoming calls, etc.)
    func pauseSpeech() {
        if speechSynthesizer.isSpeaking {
            speechSynthesizer.pauseSpeaking(at: .word)
        }
    }

    /// Resume speech
    func resumeSpeech() {
        if speechSynthesizer.isPaused {
            speechSynthesizer.continueSpeaking()
        }
    }

    // MARK: - Settings
    func setSpeechEnabled(_ enabled: Bool) {
        isSpeechEnabled = enabled
        UserDefaults.standard.set(enabled, forKey: "speech_enabled")

        if !enabled {
            stopAllAudio()
        }
    }

    func setSoundEffectsEnabled(_ enabled: Bool) {
        isSoundEffectsEnabled = enabled
        UserDefaults.standard.set(enabled, forKey: "sound_effects_enabled")

        if !enabled {
            stopAllSoundEffects()
        }
    }

    func setSpeechVolume(_ volume: Float) {
        speechVolume = max(0.0, min(1.0, volume))
        UserDefaults.standard.set(speechVolume, forKey: "speech_volume")
    }

    func setSoundEffectsVolume(_ volume: Float) {
        soundEffectsVolume = max(0.0, min(1.0, volume))
        UserDefaults.standard.set(soundEffectsVolume, forKey: "sound_effects_volume")
    }

    func setCoachingPersonality(_ personality: CoachingPersonality) {
        currentCoachingPersonality = personality
        UserDefaults.standard.set(personality.rawValue, forKey: "coaching_personality")
    }

    // MARK: - Legacy Support
    func playHapticFeedback() {
        HapticManager.shared.trigger(.selection)
    }

    // MARK: - Private Methods

    private func addToSpeechQueue(_ item: SpeechItem) {
        speechQueue.append(item)
        speechQueue.sort { $0.priority.rawValue > $1.priority.rawValue }
    }

    private func processNextSpeechItem() {
        guard !isCurrentlySpeaking, !speechQueue.isEmpty else { return }

        let nextItem = speechQueue.removeFirst()
        speakImmediately(nextItem.text)
    }

    private func speakImmediately(_ text: String) {
        let utterance = AVSpeechUtterance(string: text)
        utterance.voice = AVSpeechSynthesisVoice(language: "en-US")
        utterance.rate = 0.5
        utterance.volume = speechVolume

        isCurrentlySpeaking = true
        speechSynthesizer.speak(utterance)
    }

    private func clearSpeechQueue() {
        speechQueue.removeAll()
    }

    private func personalizeText(_ text: String, for personality: CoachingPersonality) -> String {
        switch personality {
        case .encouraging:
            return addEncouragingWords(to: text)
        case .motivational:
            return addMotivationalWords(to: text)
        case .professional:
            return text // Keep professional tone as-is
        case .friendly:
            return addFriendlyWords(to: text)
        }
    }

    private func addEncouragingWords(to text: String) -> String {
        let encouragingPrefixes = ["Great job! ", "You're doing amazing! ", "Keep it up! "]
        if Bool.random() {
            return encouragingPrefixes.randomElement()! + text
        }
        return text
    }

    private func addMotivationalWords(to text: String) -> String {
        let motivationalPrefixes = ["Push yourself! ", "You've got this! ", "Stay strong! "]
        if Bool.random() {
            return motivationalPrefixes.randomElement()! + text
        }
        return text
    }

    private func addFriendlyWords(to text: String) -> String {
        let friendlyPrefixes = ["Hey there! ", "Alright! ", "Nice! "]
        if Bool.random() {
            return friendlyPrefixes.randomElement()! + text
        }
        return text
    }

    private func generateFeedbackText(for feedback: WorkoutFeedback) -> String {
        switch feedback.type {
        case .repCompleted:
            return "Rep completed!"
        case .setCompleted:
            return "Set complete! Take a rest."
        case .formCorrection:
            return feedback.message
        case .encouragement:
            return feedback.message
        case .countdown:
            return feedback.message
        case .workoutComplete:
            return "Workout complete! Great job!"
        }
    }

    private func playSound(_ filename: String, volume: Float) async {
        guard let url = Bundle.main.url(forResource: filename, withExtension: "mp3") else {
            logger.warning("Sound file not found: \(filename)", category: .audio)
            return
        }

        do {
            let player = try AVAudioPlayer(contentsOf: url)
            player.volume = volume
            soundEffectPlayers[filename] = player
            player.play()
        } catch {
            logger.error("Failed to play sound \(filename): \(error)", category: .audio)
        }
    }

    private func stopAllSoundEffects() {
        for player in soundEffectPlayers.values {
            player.stop()
        }
        soundEffectPlayers.removeAll()
    }
}

// MARK: - AVSpeechSynthesizerDelegate
extension AudioManager: AVSpeechSynthesizerDelegate {
    nonisolated func speechSynthesizer(_ synthesizer: AVSpeechSynthesizer, didFinish utterance: AVSpeechUtterance) {
        Task { @MainActor in
            isCurrentlySpeaking = false
            processNextSpeechItem()
        }
    }

    nonisolated func speechSynthesizer(_ synthesizer: AVSpeechSynthesizer, didCancel utterance: AVSpeechUtterance) {
        Task { @MainActor in
            isCurrentlySpeaking = false
        }
    }
}

// MARK: - Supporting Types

struct SpeechItem {
    let text: String
    let priority: SpeechPriority
}

enum SpeechPriority: Int, CaseIterable {
    case low = 1
    case normal = 2
    case high = 3
    case urgent = 4
}

struct WorkoutFeedback {
    let type: FeedbackType
    let message: String
    let priority: SpeechPriority
    let shouldInterrupt: Bool

    enum FeedbackType {
        case repCompleted
        case setCompleted
        case formCorrection
        case encouragement
        case countdown
        case workoutComplete
    }
}

enum SoundEffect {
    case repComplete
    case setComplete
    case workoutStart
    case workoutComplete
    case achievement
    case countdown
    case error
    case success

    var filename: String {
        switch self {
        case .repComplete: return "rep_complete"
        case .setComplete: return "set_complete"
        case .workoutStart: return "workout_start"
        case .workoutComplete: return "workout_complete"
        case .achievement: return "achievement"
        case .countdown: return "countdown"
        case .error: return "error"
        case .success: return "success"
        }
    }
}

// MARK: - Logger Category Extension
extension Logger.Category {
    static let audio = Logger.Category(rawValue: "audio")
}