import Foundation
import ARKit
import simd

/// Comprehensive body pose data structure for AR tracking
struct BodyPoseData: Sendable, Codable {
    let timestamp: Date
    let joints: [JointName: Joint3D]
    let trackingQuality: TrackingQuality
    let confidence: Float
    let boundingBox: CGRect
    let isFullBodyVisible: Bool
    let estimatedHeight: Float?
    let frameID: Int
    let cameraTransform: simd_float4x4?

    // Legacy support properties
    let worldTransform: simd_float4x4

    init(
        timestamp: Date = Date(),
        joints: [JointName: Joint3D],
        trackingQuality: TrackingQuality,
        confidence: Float,
        boundingBox: CGRect,
        isFullBodyVisible: Bool,
        estimatedHeight: Float? = nil,
        frameID: Int = 0,
        cameraTransform: simd_float4x4? = nil,
        worldTransform: simd_float4x4 = matrix_identity_float4x4
    ) {
        self.timestamp = timestamp
        self.joints = joints
        self.trackingQuality = trackingQuality
        self.confidence = confidence
        self.boundingBox = boundingBox
        self.isFullBodyVisible = isFullBodyVisible
        self.estimatedHeight = estimatedHeight
        self.frameID = frameID
        self.cameraTransform = cameraTransform
        self.worldTransform = worldTransform
    }

    // Backward compatibility for legacy code expecting [String: Joint]
    var legacyJoints: [String: Joint] {
        var legacyDict: [String: Joint] = [:]
        for (jointName, joint3D) in joints {
            legacyDict[jointName.rawValue] = Joint(
                position: joint3D.position,
                confidence: joint3D.confidence,
                timestamp: timestamp
            )
        }
        return legacyDict
    }

    // Legacy timestamp as TimeInterval
    var timeInterval: TimeInterval {
        return timestamp.timeIntervalSince1970
    }

    // Legacy frameID as UUID
    var frameUUID: UUID {
        return UUID() // Generate new UUID for compatibility
    }
}

// MARK: - Joint Data
struct Joint3D: Sendable {
    let position: simd_float3
    let confidence: Float
    let isTracked: Bool
    let parentJoint: JointName?
    let childJoints: [JointName]
    
    init(
        position: simd_float3,
        confidence: Float,
        isTracked: Bool = true,
        parentJoint: JointName? = nil,
        childJoints: [JointName] = []
    ) {
        self.position = position
        self.confidence = confidence
        self.isTracked = isTracked
        self.parentJoint = parentJoint
        self.childJoints = childJoints
    }
    
    /// Distance to another joint
    func distance(to other: Joint3D) -> Float {
        return simd_distance(position, other.position)
    }
    
    /// Angle between this joint and two other joints
    func angle(to joint1: Joint3D, and joint2: Joint3D) -> Float {
        let vector1 = joint1.position - position
        let vector2 = joint2.position - position
        
        let dot = simd_dot(vector1, vector2)
        let magnitude1 = simd_length(vector1)
        let magnitude2 = simd_length(vector2)
        
        guard magnitude1 > 0 && magnitude2 > 0 else { return 0 }
        
        let cosAngle = dot / (magnitude1 * magnitude2)
        return acos(max(-1, min(1, cosAngle)))
    }
}

// MARK: - Legacy Joint Support
/// Legacy Joint structure for backward compatibility
struct Joint: Sendable, Codable {
    let position: simd_float3
    let confidence: Float
    let timestamp: Date

    init(position: simd_float3, confidence: Float, timestamp: Date = Date()) {
        self.position = position
        self.confidence = confidence
        self.timestamp = timestamp
    }
}

// MARK: - Joint Names
enum JointName: String, CaseIterable, Sendable {
    // Head and Neck
    case head = "head"
    case neck = "neck"
    
    // Torso
    case root = "root"
    case spine1 = "spine1"
    case spine2 = "spine2"
    case spine3 = "spine3"
    case spine4 = "spine4"
    case spine5 = "spine5"
    case spine6 = "spine6"
    case spine7 = "spine7"
    
    // Left Arm
    case leftShoulder = "left_shoulder"
    case leftUpperArm = "left_upper_arm"
    case leftLowerArm = "left_lower_arm"
    case leftHand = "left_hand"
    
    // Right Arm
    case rightShoulder = "right_shoulder"
    case rightUpperArm = "right_upper_arm"
    case rightLowerArm = "right_lower_arm"
    case rightHand = "right_hand"
    
    // Left Leg
    case leftUpperLeg = "left_upper_leg"
    case leftLowerLeg = "left_lower_leg"
    case leftFoot = "left_foot"
    case leftToes = "left_toes"
    
    // Right Leg
    case rightUpperLeg = "right_upper_leg"
    case rightLowerLeg = "right_lower_leg"
    case rightFoot = "right_foot"
    case rightToes = "right_toes"
    
    var displayName: String {
        switch self {
        case .head: return "Head"
        case .neck: return "Neck"
        case .root: return "Hip Center"
        case .spine1: return "Lower Spine"
        case .spine2: return "Mid Spine"
        case .spine3: return "Upper Spine"
        case .spine4: return "Lower Chest"
        case .spine5: return "Mid Chest"
        case .spine6: return "Upper Chest"
        case .spine7: return "Neck Base"
        case .leftShoulder: return "Left Shoulder"
        case .leftUpperArm: return "Left Upper Arm"
        case .leftLowerArm: return "Left Lower Arm"
        case .leftHand: return "Left Hand"
        case .rightShoulder: return "Right Shoulder"
        case .rightUpperArm: return "Right Upper Arm"
        case .rightLowerArm: return "Right Lower Arm"
        case .rightHand: return "Right Hand"
        case .leftUpperLeg: return "Left Thigh"
        case .leftLowerLeg: return "Left Shin"
        case .leftFoot: return "Left Foot"
        case .leftToes: return "Left Toes"
        case .rightUpperLeg: return "Right Thigh"
        case .rightLowerLeg: return "Right Shin"
        case .rightFoot: return "Right Foot"
        case .rightToes: return "Right Toes"
        }
    }
    
    var isLeftSide: Bool {
        return rawValue.contains("left")
    }
    
    var isRightSide: Bool {
        return rawValue.contains("right")
    }
    
    var isCore: Bool {
        return [.root, .spine1, .spine2, .spine3, .spine4, .spine5, .spine6, .spine7].contains(self)
    }
    
    var isArm: Bool {
        return [.leftShoulder, .leftUpperArm, .leftLowerArm, .leftHand,
                .rightShoulder, .rightUpperArm, .rightLowerArm, .rightHand].contains(self)
    }
    
    var isLeg: Bool {
        return [.leftUpperLeg, .leftLowerLeg, .leftFoot, .leftToes,
                .rightUpperLeg, .rightLowerLeg, .rightFoot, .rightToes].contains(self)
    }
}

// MARK: - Tracking Quality
enum TrackingQuality: String, Sendable {
    case initializing = "initializing"
    case poor = "poor"
    case limited = "limited"
    case good = "good"
    case excellent = "excellent"
    
    var displayName: String {
        switch self {
        case .initializing: return "Initializing"
        case .poor: return "Poor"
        case .limited: return "Limited"
        case .good: return "Good"
        case .excellent: return "Excellent"
        }
    }
    
    var color: String {
        switch self {
        case .initializing: return "gray"
        case .poor: return "red"
        case .limited: return "orange"
        case .good: return "yellow"
        case .excellent: return "green"
        }
    }
    
    var confidenceThreshold: Float {
        switch self {
        case .initializing: return 0.0
        case .poor: return 0.3
        case .limited: return 0.5
        case .good: return 0.7
        case .excellent: return 0.9
        }
    }
}

// MARK: - Pose Analysis Extensions
extension BodyPoseData {
    /// Calculate the angle at a specific joint
    func jointAngle(at joint: JointName, using parentJoint: JointName, and childJoint: JointName) -> Float? {
        guard let currentJoint = joints[joint],
              let parent = joints[parentJoint],
              let child = joints[childJoint] else {
            return nil
        }
        
        return currentJoint.angle(to: parent, and: child)
    }
    
    /// Get the center of mass based on tracked joints
    var centerOfMass: simd_float3? {
        let coreJoints = joints.filter { $0.key.isCore }
        guard !coreJoints.isEmpty else { return nil }
        
        let totalPosition = coreJoints.reduce(simd_float3(0, 0, 0)) { result, joint in
            return result + joint.value.position
        }
        
        return totalPosition / Float(coreJoints.count)
    }
    
    /// Check if the pose is stable (low movement between frames)
    func isStable(comparedTo previousPose: BodyPoseData, threshold: Float = 0.05) -> Bool {
        let keyJoints: [JointName] = [.root, .leftShoulder, .rightShoulder, .leftUpperLeg, .rightUpperLeg]
        
        for jointName in keyJoints {
            guard let currentJoint = joints[jointName],
                  let previousJoint = previousPose.joints[jointName] else {
                continue
            }
            
            let distance = currentJoint.distance(to: previousJoint)
            if distance > threshold {
                return false
            }
        }
        
        return true
    }
    
    /// Calculate overall pose confidence
    var overallConfidence: Float {
        guard !joints.isEmpty else { return 0.0 }
        
        let totalConfidence = joints.values.reduce(0.0) { $0 + $1.confidence }
        return totalConfidence / Float(joints.count)
    }
    
    /// Check if critical joints are being tracked
    var hasCriticalJoints: Bool {
        let criticalJoints: [JointName] = [.root, .leftShoulder, .rightShoulder, .leftUpperLeg, .rightUpperLeg]
        return criticalJoints.allSatisfy { joints[$0]?.isTracked == true }
    }
}

// MARK: - Pose Validation
extension BodyPoseData {
    /// Validate that the pose data is reasonable
    var isValid: Bool {
        return confidence > 0.3 && 
               hasCriticalJoints && 
               trackingQuality != .poor &&
               !joints.isEmpty
    }
    
    /// Check for anatomically impossible poses
    var isAnatomicallyValid: Bool {
        // Check for reasonable joint distances
        guard let leftShoulder = joints[.leftShoulder],
              let rightShoulder = joints[.rightShoulder] else {
            return false
        }
        
        let shoulderDistance = leftShoulder.distance(to: rightShoulder)
        
        // Shoulder distance should be reasonable (20-80cm)
        return shoulderDistance > 0.2 && shoulderDistance < 0.8
    }
}
