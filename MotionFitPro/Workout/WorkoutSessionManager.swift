import Foundation
import Combine
import SwiftUI

// MARK: - Workout Session Manager

/// <PERSON><PERSON> complete workout sessions with AR tracking, ML analysis, and real-time feedback
@MainActor
final class WorkoutSessionManager: ObservableObject {
    
    // MARK: - Published Properties
    
    @Published var currentSession: WorkoutSession?
    @Published var sessionState: WorkoutSessionState = .notStarted
    @Published var currentExercise: ExerciseType = .unknown
    @Published var repCount = 0
    @Published var setCount = 0
    @Published var currentFormScore: Float = 0.0
    @Published var realtimeFeedback: [FormFeedback] = []
    @Published var sessionStats = SessionStats()
    
    // MARK: - Dependencies (Injected)
    
    @Injected private var arTrackingService: ARTrackingServiceProtocol
    @Injected private var mlProcessingService: MLProcessingServiceProtocol
    @Injected private var feedbackService: FeedbackServiceProtocol
    @Injected private var coachingService: CoachingServiceProtocol
    @Injected private var analyticsService: AnalyticsServiceProtocol
    @Injected private var hapticService: HapticServiceProtocol
    @Injected private var audioService: AudioServiceProtocol
    @Injected private var workoutRepository: WorkoutRepositoryProtocol
    @Injected private var userProfileRepository: UserProfileRepositoryProtocol
    
    private let logger = Logger()
    
    // MARK: - Private Properties
    
    private var cancellables = Set<AnyCancellable>()
    private var poseProcessingTask: Task<Void, Never>?
    
    // Exercise tracking
    private var exerciseHistory: [ExerciseAnalysis] = []
    private var repDetector = RepetitionDetector()
    private var formTracker = FormTracker()
    
    // Session timing
    private var sessionStartTime: Date?
    private var exerciseStartTime: Date?
    
    // User preferences
    private var userProfile: UserProfile?
    private var coachingPersonality: CoachingPersonality = .encouraging
    
    // MARK: - Initialization
    
    init() {
        setupBindings()
        loadUserProfile()
    }
    
    deinit {
        stopSession()
    }
    
    // MARK: - Setup
    
    private func setupBindings() {
        // Subscribe to AR pose data
        arTrackingService.bodyPosePublisher
            .receive(on: DispatchQueue.main)
            .sink { [weak self] poseData in
                self?.processPoseData(poseData)
            }
            .store(in: &cancellables)
    }
    
    private func loadUserProfile() {
        Task {
            do {
                userProfile = try await userProfileRepository.getCurrentProfile()
                coachingPersonality = userProfile?.preferredCoachingStyle ?? .encouraging
                logger.info("User profile loaded", category: .workout)
            } catch {
                logger.error("Failed to load user profile: \(error)", category: .workout)
            }
        }
    }
    
    // MARK: - Session Management
    
    func startSession(exercises: [ExerciseType] = []) async throws {
        guard sessionState == .notStarted else {
            throw WorkoutError.sessionAlreadyActive
        }
        
        // Create new session
        let session = WorkoutSession(
            userId: userProfile?.id ?? UUID(),
            exercises: exercises,
            startTime: Date()
        )
        
        currentSession = session
        sessionState = .active
        sessionStartTime = Date()
        
        // Start AR tracking
        try await arTrackingService.startTracking()
        
        // Track analytics
        analyticsService.trackWorkoutStart(session)
        
        // Provide audio feedback
        audioService.speak("Workout started. Let's begin!", priority: .normal)
        hapticService.triggerSuccess()
        
        logger.info("Workout session started", category: .workout)
    }
    
    func pauseSession() {
        guard sessionState == .active else { return }
        
        sessionState = .paused
        arTrackingService.pauseTracking()
        
        audioService.speak("Workout paused", priority: .normal)
        logger.info("Workout session paused", category: .workout)
    }
    
    func resumeSession() {
        guard sessionState == .paused else { return }
        
        sessionState = .active
        arTrackingService.resumeTracking()
        
        audioService.speak("Workout resumed", priority: .normal)
        logger.info("Workout session resumed", category: .workout)
    }
    
    func stopSession() async {
        guard let session = currentSession else { return }
        
        // Stop AR tracking
        arTrackingService.stopTracking()
        
        // Finalize session
        session.endTime = Date()
        session.duration = sessionStartTime.map { Date().timeIntervalSince($0) }
        
        // Save session
        do {
            try await workoutRepository.saveWorkout(session)
            analyticsService.trackWorkoutComplete(session)
        } catch {
            logger.error("Failed to save workout session: \(error)", category: .workout)
        }
        
        // Reset state
        sessionState = .completed
        currentSession = nil
        resetSessionData()
        
        // Provide completion feedback
        let completionMessage = generateCompletionMessage()
        audioService.speak(completionMessage, priority: .high)
        hapticService.triggerSuccess()
        
        logger.info("Workout session completed", category: .workout)
    }
    
    // MARK: - Pose Data Processing
    
    private func processPoseData(_ poseData: BodyPoseData) {
        guard sessionState == .active else { return }
        
        // Cancel previous processing task if still running
        poseProcessingTask?.cancel()
        
        // Process pose data asynchronously
        poseProcessingTask = Task {
            do {
                let analysis = try await mlProcessingService.processBodyPose(poseData)
                await handleExerciseAnalysis(analysis)
            } catch {
                logger.error("Failed to process pose data: \(error)", category: .workout)
            }
        }
    }
    
    private func handleExerciseAnalysis(_ analysis: ExerciseAnalysis) async {
        // Update current exercise if detected
        if analysis.exercise != .unknown && analysis.confidence > 0.7 {
            if currentExercise != analysis.exercise {
                await transitionToExercise(analysis.exercise)
            }
            currentExercise = analysis.exercise
        }
        
        // Update form score
        currentFormScore = Float(analysis.formScore)
        
        // Track exercise history
        exerciseHistory.append(analysis)
        
        // Detect repetitions
        if let repDetection = repDetector.processAnalysis(analysis) {
            await handleRepetitionDetected(repDetection)
        }
        
        // Track form over time
        formTracker.addAnalysis(analysis)
        
        // Generate real-time feedback
        await generateRealtimeFeedback(analysis)
        
        // Update session stats
        updateSessionStats(analysis)
    }
    
    // MARK: - Exercise Transitions
    
    private func transitionToExercise(_ exercise: ExerciseType) async {
        let previousExercise = currentExercise
        
        if previousExercise != .unknown {
            logger.info("Exercise transition: \(previousExercise.displayName) → \(exercise.displayName)", category: .workout)
        }
        
        // Reset counters for new exercise
        repCount = 0
        setCount = 0
        exerciseStartTime = Date()
        
        // Clear previous feedback
        realtimeFeedback.removeAll()
        
        // Provide transition feedback
        if previousExercise != .unknown {
            audioService.speak("Switching to \(exercise.displayName)", priority: .normal)
        } else {
            audioService.speak("Starting \(exercise.displayName)", priority: .normal)
        }
        
        // Reset detectors for new exercise
        repDetector.reset(for: exercise)
        formTracker.reset()
    }
    
    // MARK: - Repetition Handling
    
    private func handleRepetitionDetected(_ detection: RepetitionDetection) async {
        repCount += 1
        
        // Update session
        currentSession?.addRepetition(exercise: currentExercise, formScore: detection.formScore)
        
        // Generate rep completion feedback
        let feedback = generateRepCompletionFeedback(detection)
        await feedbackService.deliverFeedback(feedback)
        
        // Audio feedback
        let message = coachingService.generateCoachingMessage(
            for: .repCompletion(repNumber: repCount, setNumber: setCount, formScore: detection.formScore),
            personality: coachingPersonality
        )
        audioService.speak(message, priority: .normal)
        
        // Haptic feedback
        hapticService.triggerSuccess()
        
        logger.info("Rep completed: \(repCount), form score: \(detection.formScore)", category: .workout)
    }
    
    // MARK: - Feedback Generation
    
    private func generateRealtimeFeedback(_ analysis: ExerciseAnalysis) async {
        // Generate feedback for form issues
        for issue in analysis.detectedIssues {
            let feedback = feedbackService.generateFeedback(for: analysis, personality: coachingPersonality)
            
            // Add to realtime feedback (limit to recent items)
            realtimeFeedback.append(feedback)
            if realtimeFeedback.count > 5 {
                realtimeFeedback.removeFirst()
            }
            
            // Deliver critical feedback immediately
            if issue.severity == .high || issue.severity == .critical {
                await feedbackService.deliverFeedback(feedback)
            }
        }
        
        // Generate encouragement for good form
        if analysis.formScore > 0.8 && analysis.detectedIssues.isEmpty {
            let encouragement = FormFeedback.success("Great Form!", message: "Keep it up!")
            realtimeFeedback.append(encouragement)
        }
    }
    
    private func generateRepCompletionFeedback(_ detection: RepetitionDetection) -> FormFeedback {
        let title = "Rep \(repCount) Complete"
        let message: String
        
        if detection.formScore > 0.8 {
            message = "Excellent form!"
        } else if detection.formScore > 0.6 {
            message = "Good rep, focus on form"
        } else {
            message = "Rep completed, improve form"
        }
        
        return FormFeedback(
            type: .achievement,
            message: message,
            title: title,
            severity: .low,
            duration: 2.0,
            deliveryMethods: [.visual, .haptic]
        )
    }
    
    private func generateCompletionMessage() -> String {
        guard let session = currentSession else {
            return "Workout completed!"
        }
        
        let duration = session.duration ?? 0
        let minutes = Int(duration) / 60
        let totalReps = session.exercises.values.reduce(0) { $0 + $1.count }
        
        return "Workout completed! \(minutes) minutes, \(totalReps) reps. Great job!"
    }
    
    // MARK: - Statistics
    
    private func updateSessionStats(_ analysis: ExerciseAnalysis) {
        sessionStats = SessionStats(
            duration: sessionStartTime.map { Date().timeIntervalSince($0) } ?? 0,
            totalReps: repCount,
            averageFormScore: calculateAverageFormScore(),
            exercisesPerformed: Set([currentExercise]).count,
            caloriesEstimated: estimateCaloriesBurned()
        )
    }
    
    private func calculateAverageFormScore() -> Float {
        guard !exerciseHistory.isEmpty else { return 0.0 }
        
        let totalScore = exerciseHistory.map { Float($0.formScore) }.reduce(0, +)
        return totalScore / Float(exerciseHistory.count)
    }
    
    private func estimateCaloriesBurned() -> Int {
        guard let duration = sessionStartTime?.timeIntervalSince(Date()) else { return 0 }
        
        let minutes = abs(duration) / 60.0
        let caloriesPerMinute = currentExercise.caloriesPerMinute
        
        return Int(minutes * caloriesPerMinute)
    }
    
    // MARK: - Utility Methods
    
    private func resetSessionData() {
        repCount = 0
        setCount = 0
        currentExercise = .unknown
        currentFormScore = 0.0
        realtimeFeedback.removeAll()
        exerciseHistory.removeAll()
        sessionStats = SessionStats()
        
        repDetector.reset()
        formTracker.reset()
    }
}

// MARK: - Supporting Types

enum WorkoutSessionState {
    case notStarted
    case active
    case paused
    case completed
}

struct SessionStats {
    let duration: TimeInterval
    let totalReps: Int
    let averageFormScore: Float
    let exercisesPerformed: Int
    let caloriesEstimated: Int
    
    init() {
        self.duration = 0
        self.totalReps = 0
        self.averageFormScore = 0.0
        self.exercisesPerformed = 0
        self.caloriesEstimated = 0
    }
    
    init(duration: TimeInterval, totalReps: Int, averageFormScore: Float, exercisesPerformed: Int, caloriesEstimated: Int) {
        self.duration = duration
        self.totalReps = totalReps
        self.averageFormScore = averageFormScore
        self.exercisesPerformed = exercisesPerformed
        self.caloriesEstimated = caloriesEstimated
    }
}

enum WorkoutError: LocalizedError {
    case sessionAlreadyActive
    case noActiveSession
    case trackingNotAvailable

    var errorDescription: String? {
        switch self {
        case .sessionAlreadyActive:
            return "A workout session is already active"
        case .noActiveSession:
            return "No active workout session"
        case .trackingNotAvailable:
            return "AR tracking is not available"
        }
    }
}

// MARK: - Repetition Detector

/// Detects exercise repetitions based on movement phases and biomechanics
final class RepetitionDetector {

    private var currentExercise: ExerciseType = .unknown
    private var phaseHistory: [MovementPhase] = []
    private var analysisHistory: [ExerciseAnalysis] = []
    private let maxHistorySize = 10

    func processAnalysis(_ analysis: ExerciseAnalysis) -> RepetitionDetection? {
        // Add to history
        analysisHistory.append(analysis)
        phaseHistory.append(analysis.repPhase)

        // Maintain history size
        if analysisHistory.count > maxHistorySize {
            analysisHistory.removeFirst()
            phaseHistory.removeFirst()
        }

        // Detect repetition completion
        return detectRepetitionCompletion()
    }

    private func detectRepetitionCompletion() -> RepetitionDetection? {
        guard phaseHistory.count >= 4 else { return nil }

        // Look for complete movement cycle
        let recentPhases = Array(phaseHistory.suffix(4))

        // Check for typical rep pattern: start -> eccentric -> bottom -> concentric -> start
        if isCompleteRepetition(recentPhases) {
            let recentAnalyses = Array(analysisHistory.suffix(4))
            let averageFormScore = recentAnalyses.map { Float($0.formScore) }.reduce(0, +) / Float(recentAnalyses.count)

            return RepetitionDetection(
                exercise: currentExercise,
                formScore: averageFormScore,
                timestamp: Date(),
                phases: recentPhases
            )
        }

        return nil
    }

    private func isCompleteRepetition(_ phases: [MovementPhase]) -> Bool {
        // Simplified repetition detection logic
        // Look for a cycle that includes eccentric and concentric phases
        return phases.contains(.eccentric) && phases.contains(.concentric)
    }

    func reset(for exercise: ExerciseType = .unknown) {
        currentExercise = exercise
        phaseHistory.removeAll()
        analysisHistory.removeAll()
    }

    func reset() {
        reset(for: .unknown)
    }
}

// MARK: - Form Tracker

/// Tracks form quality over time and identifies trends
final class FormTracker {

    private var formHistory: [Float] = []
    private var issueHistory: [FormIssue] = []
    private let maxHistorySize = 50

    func addAnalysis(_ analysis: ExerciseAnalysis) {
        formHistory.append(Float(analysis.formScore))
        issueHistory.append(contentsOf: analysis.detectedIssues)

        // Maintain history size
        if formHistory.count > maxHistorySize {
            formHistory.removeFirst()
        }

        if issueHistory.count > maxHistorySize {
            issueHistory.removeFirst()
        }
    }

    func getFormTrend() -> FormTrend {
        guard formHistory.count >= 5 else { return .stable }

        let recent = Array(formHistory.suffix(5))
        let earlier = Array(formHistory.prefix(5))

        let recentAvg = recent.reduce(0, +) / Float(recent.count)
        let earlierAvg = earlier.reduce(0, +) / Float(earlier.count)

        let difference = recentAvg - earlierAvg

        if difference > 0.1 {
            return .improving
        } else if difference < -0.1 {
            return .declining
        } else {
            return .stable
        }
    }

    func getAverageFormScore() -> Float {
        guard !formHistory.isEmpty else { return 0.0 }
        return formHistory.reduce(0, +) / Float(formHistory.count)
    }

    func getMostCommonIssues() -> [FormCriteria] {
        let issueCounts = Dictionary(grouping: issueHistory, by: { $0.criteria })
            .mapValues { $0.count }

        return issueCounts.sorted { $0.value > $1.value }
            .prefix(3)
            .map { $0.key }
    }

    func reset() {
        formHistory.removeAll()
        issueHistory.removeAll()
    }
}

// MARK: - Supporting Types

struct RepetitionDetection {
    let exercise: ExerciseType
    let formScore: Float
    let timestamp: Date
    let phases: [MovementPhase]
}

enum FormTrend {
    case improving
    case stable
    case declining

    var displayName: String {
        switch self {
        case .improving: return "Improving"
        case .stable: return "Stable"
        case .declining: return "Declining"
        }
    }
}
