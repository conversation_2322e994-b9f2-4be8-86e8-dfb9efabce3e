import Foundation
import SwiftUI

// MARK: - Unified Form Feedback Model

/// Comprehensive feedback model for exercise form and performance
struct FormFeedback: Identifiable, Sendable {
    let id = UUID()
    let type: FeedbackType
    let message: String
    let title: String?
    let severity: FeedbackSeverity
    let exerciseType: ExerciseType?
    let timestamp: Date
    let duration: TimeInterval
    let deliveryMethods: Set<FeedbackDeliveryMethod>
    let context: FeedbackContext?
    let actionable: Bool
    let priority: Int
    let metadata: [String: String]
    
    // MARK: - Initializers
    
    init(
        type: FeedbackType,
        message: String,
        title: String? = nil,
        severity: FeedbackSeverity = .medium,
        exerciseType: ExerciseType? = nil,
        duration: TimeInterval = 3.0,
        deliveryMethods: Set<FeedbackDeliveryMethod> = [.visual, .audio],
        context: FeedbackContext? = nil,
        actionable: Bool = false,
        metadata: [String: String] = [:]
    ) {
        self.type = type
        self.message = message
        self.title = title
        self.severity = severity
        self.exerciseType = exerciseType
        self.timestamp = Date()
        self.duration = duration
        self.deliveryMethods = deliveryMethods
        self.context = context
        self.actionable = actionable
        self.priority = severity.priority
        self.metadata = metadata
    }
    
    // MARK: - Convenience Initializers
    
    static func correction(_ message: String, for exercise: ExerciseType? = nil, severity: FeedbackSeverity = .medium) -> FormFeedback {
        return FormFeedback(
            type: .correction,
            message: message,
            title: "Form Correction",
            severity: severity,
            exerciseType: exercise,
            deliveryMethods: [.visual, .audio, .haptic],
            context: .formCorrection,
            actionable: true
        )
    }
    
    static func encouragement(_ message: String, title: String = "Great Job!") -> FormFeedback {
        return FormFeedback(
            type: .encouragement,
            message: message,
            title: title,
            severity: .low,
            deliveryMethods: [.visual, .haptic],
            context: .encouragement
        )
    }
    
    static func safety(_ message: String, severity: FeedbackSeverity = .high) -> FormFeedback {
        return FormFeedback(
            type: .safety,
            message: message,
            title: "Safety Alert",
            severity: severity,
            deliveryMethods: [.visual, .audio, .haptic],
            context: .safety,
            actionable: true
        )
    }
    
    static func achievement(_ message: String, title: String) -> FormFeedback {
        return FormFeedback(
            type: .achievement,
            message: message,
            title: title,
            severity: .low,
            deliveryMethods: [.visual, .audio, .haptic],
            context: .achievement
        )
    }
    
    static func instruction(_ message: String, for exercise: ExerciseType) -> FormFeedback {
        return FormFeedback(
            type: .instruction,
            message: message,
            title: "Exercise Instruction",
            severity: .medium,
            exerciseType: exercise,
            deliveryMethods: [.visual, .audio],
            context: .instruction,
            actionable: true
        )
    }
    
    static func warning(_ message: String, title: String = "Warning") -> FormFeedback {
        return FormFeedback(
            type: .warning,
            message: message,
            title: title,
            severity: .high,
            deliveryMethods: [.visual, .audio, .haptic],
            context: .warning,
            actionable: true
        )
    }
    
    static func success(_ title: String, message: String) -> FormFeedback {
        return FormFeedback(
            type: .success,
            message: message,
            title: title,
            severity: .low,
            deliveryMethods: [.visual, .haptic],
            context: .excellentForm
        )
    }
    
    // MARK: - Computed Properties
    
    var isUrgent: Bool {
        return severity == .critical || severity == .high
    }
    
    var shouldInterrupt: Bool {
        return severity.shouldInterrupt
    }
    
    var displayColor: Color {
        return severity.color
    }
    
    var iconName: String {
        return type.iconName
    }
    
    var shouldAutoHide: Bool {
        return !actionable && severity != .critical
    }
    
    var effectiveDuration: TimeInterval {
        switch severity {
        case .critical: return 10.0
        case .high: return 5.0
        case .medium: return 3.0
        case .low: return 2.0
        }
    }
}

// MARK: - Feedback Type

/// Types of feedback that can be provided
enum FeedbackType: String, CaseIterable, Sendable {
    case correction = "correction"
    case encouragement = "encouragement"
    case safety = "safety"
    case achievement = "achievement"
    case instruction = "instruction"
    case warning = "warning"
    case success = "success"
    case information = "information"
    case motivation = "motivation"
    case celebration = "celebration"
    
    var displayName: String {
        switch self {
        case .correction: return "Form Correction"
        case .encouragement: return "Encouragement"
        case .safety: return "Safety Alert"
        case .achievement: return "Achievement"
        case .instruction: return "Instruction"
        case .warning: return "Warning"
        case .success: return "Success"
        case .information: return "Information"
        case .motivation: return "Motivation"
        case .celebration: return "Celebration"
        }
    }
    
    var iconName: String {
        switch self {
        case .correction: return "exclamationmark.triangle.fill"
        case .encouragement: return "hand.thumbsup.fill"
        case .safety: return "shield.fill"
        case .achievement: return "star.fill"
        case .instruction: return "info.circle.fill"
        case .warning: return "exclamationmark.octagon.fill"
        case .success: return "checkmark.circle.fill"
        case .information: return "info.circle"
        case .motivation: return "flame.fill"
        case .celebration: return "party.popper.fill"
        }
    }
    
    var defaultSeverity: FeedbackSeverity {
        switch self {
        case .safety, .warning: return .high
        case .correction: return .medium
        case .instruction, .information: return .medium
        case .encouragement, .success, .achievement, .motivation, .celebration: return .low
        }
    }
    
    var shouldPersist: Bool {
        switch self {
        case .safety, .warning, .correction: return true
        case .instruction, .information: return true
        case .encouragement, .success, .achievement, .motivation, .celebration: return false
        }
    }
}

// MARK: - Feedback Delivery Method

/// Methods for delivering feedback to the user
enum FeedbackDeliveryMethod: String, CaseIterable, Sendable {
    case visual = "visual"
    case audio = "audio"
    case haptic = "haptic"
    case notification = "notification"
    
    var displayName: String {
        switch self {
        case .visual: return "Visual"
        case .audio: return "Audio"
        case .haptic: return "Haptic"
        case .notification: return "Notification"
        }
    }
    
    var isRealtime: Bool {
        switch self {
        case .visual, .audio, .haptic: return true
        case .notification: return false
        }
    }
}

// MARK: - Form Analysis

/// Comprehensive analysis of exercise form
struct FormAnalysis: Sendable {
    let overallScore: Float
    let scores: [FormCriteria: Float]
    let issues: [FormIssue]
    let recommendations: [String]
    let exerciseType: ExerciseType
    let confidence: Float
    let timestamp: Date
    let metadata: [String: Any]
    
    init(
        overallScore: Float,
        scores: [FormCriteria: Float] = [:],
        issues: [FormIssue] = [],
        recommendations: [String] = [],
        exerciseType: ExerciseType = .unknown,
        confidence: Float = 0.8,
        metadata: [String: Any] = [:]
    ) {
        self.overallScore = max(0.0, min(1.0, overallScore))
        self.scores = scores
        self.issues = issues
        self.recommendations = recommendations
        self.exerciseType = exerciseType
        self.confidence = max(0.0, min(1.0, confidence))
        self.timestamp = Date()
        self.metadata = metadata
    }
    
    // MARK: - Computed Properties
    
    var grade: FormGrade {
        switch overallScore {
        case 0.9...1.0: return .excellent
        case 0.8..<0.9: return .good
        case 0.7..<0.8: return .fair
        case 0.6..<0.7: return .poor
        default: return .critical
        }
    }
    
    var hasIssues: Bool {
        return !issues.isEmpty
    }
    
    var hasCriticalIssues: Bool {
        return issues.contains { $0.severity == .critical }
    }
    
    var hasHighPriorityIssues: Bool {
        return issues.contains { $0.severity == .high || $0.severity == .critical }
    }
    
    var primaryIssue: FormIssue? {
        return issues.max { $0.severity.priority < $1.severity.priority }
    }
    
    var improvementAreas: [FormCriteria] {
        return scores.compactMap { criteria, score in
            score < 0.7 ? criteria : nil
        }.sorted { scores[$0] ?? 0 < scores[$1] ?? 0 }
    }
    
    var strengths: [FormCriteria] {
        return scores.compactMap { criteria, score in
            score >= 0.8 ? criteria : nil
        }.sorted { scores[$0] ?? 0 > scores[$1] ?? 0 }
    }
}

// MARK: - Form Grade

/// Letter grade representation of form quality
enum FormGrade: String, CaseIterable, Sendable {
    case excellent = "A"
    case good = "B"
    case fair = "C"
    case poor = "D"
    case critical = "F"
    
    var displayName: String {
        switch self {
        case .excellent: return "Excellent"
        case .good: return "Good"
        case .fair: return "Fair"
        case .poor: return "Poor"
        case .critical: return "Critical"
        }
    }
    
    var color: Color {
        switch self {
        case .excellent: return .green
        case .good: return .blue
        case .fair: return .yellow
        case .poor: return .orange
        case .critical: return .red
        }
    }
    
    var description: String {
        switch self {
        case .excellent: return "Outstanding form! Keep it up!"
        case .good: return "Good form with minor areas for improvement"
        case .fair: return "Decent form but needs attention"
        case .poor: return "Form needs significant improvement"
        case .critical: return "Critical form issues - focus on safety"
        }
    }
}

// MARK: - Exercise Analysis

/// Complete analysis of an exercise performance
struct ExerciseAnalysis: Identifiable, Sendable {
    let id = UUID()
    let exercise: ExerciseType
    let timestamp: Date
    let formScore: Double
    let repPhase: MovementPhase
    let poseData: BodyPoseData
    let detectedIssues: [FormIssue]
    let safetyRisk: SafetyRisk
    let confidence: Float
    let formAnalysis: FormAnalysis
    let metadata: [String: Any]
    
    init(
        exercise: ExerciseType,
        timestamp: Date = Date(),
        formScore: Double,
        repPhase: MovementPhase,
        poseData: BodyPoseData,
        detectedIssues: [FormIssue] = [],
        safetyRisk: SafetyRisk = .none,
        confidence: Float = 0.8,
        formAnalysis: FormAnalysis? = nil,
        metadata: [String: Any] = [:]
    ) {
        self.exercise = exercise
        self.timestamp = timestamp
        self.formScore = max(0.0, min(1.0, formScore))
        self.repPhase = repPhase
        self.poseData = poseData
        self.detectedIssues = detectedIssues
        self.safetyRisk = safetyRisk
        self.confidence = max(0.0, min(1.0, confidence))
        self.formAnalysis = formAnalysis ?? FormAnalysis(
            overallScore: Float(formScore),
            issues: detectedIssues,
            exerciseType: exercise,
            confidence: confidence
        )
        self.metadata = metadata
    }
    
    // MARK: - Computed Properties
    
    var isGoodForm: Bool {
        return formScore >= 0.8
    }
    
    var needsCorrection: Bool {
        return formScore < 0.7 || !detectedIssues.isEmpty
    }
    
    var isSafe: Bool {
        return safetyRisk == .none || safetyRisk == .low
    }
    
    var shouldStopExercise: Bool {
        return safetyRisk.shouldStopExercise
    }
    
    var grade: FormGrade {
        return formAnalysis.grade
    }
}
