import Foundation
import SwiftData

// ExerciseType is now defined in Data/Models/SwiftData/ExerciseType.swift

@Model
class ExercisePerformance {
    @Attribute(.unique) var id: UUID
    var workoutSessionID: UUID
    var exerciseType: ExerciseType
    var startTime: Date
    var endTime: Date?
    var duration: TimeInterval
    var totalReps: Int
    var completedSets: Int
    var targetSets: Int
    var averageFormScore: Double
    var bestFormScore: Double
    var worstFormScore: Double
    var caloriesBurned: Double
    var status: ExerciseStatus
    var createdDate: Date
    var lastModified: Date
    
    @Relationship(deleteRule: .cascade) var setPerformances: [SetPerformance] = []
    
    init(workoutSessionID: UUID, exerciseType: ExerciseType, targetSets: Int) {
        self.id = UUID()
        self.workoutSessionID = workoutSessionID
        self.exerciseType = exerciseType
        self.startTime = Date()
        self.duration = 0
        self.totalReps = 0
        self.completedSets = 0
        self.targetSets = targetSets
        self.averageFormScore = 0.0
        self.bestFormScore = 0.0
        self.worstFormScore = 100.0
        self.caloriesBurned = 0.0
        self.status = .inProgress
        self.createdDate = Date()
        self.lastModified = Date()
    }
}

enum ExerciseStatus: String, CaseIterable, Codable {
    case inProgress = "inProgress"
    case completed = "completed"
    case skipped = "skipped"
}