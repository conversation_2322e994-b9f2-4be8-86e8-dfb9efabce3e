import Foundation
import Combine

// MARK: - Dependency Injection Container

/// A lightweight dependency injection container for managing service dependencies
@MainActor
final class DIContainer: ObservableObject {
    static let shared = DIContainer()
    
    private var services: [String: Any] = [:]
    private var factories: [String: () -> Any] = [:]
    private var singletons: [String: Any] = [:]
    
    private init() {}
    
    // MARK: - Registration
    
    /// Register a singleton service
    func registerSingleton<T>(_ type: T.Type, factory: @escaping () -> T) {
        let key = String(describing: type)
        factories[key] = factory
    }
    
    /// Register a transient service (new instance each time)
    func registerTransient<T>(_ type: T.Type, factory: @escaping () -> T) {
        let key = String(describing: type)
        factories[key] = factory
    }
    
    /// Register an existing instance as singleton
    func registerInstance<T>(_ instance: T, for type: T.Type) {
        let key = String(describing: type)
        singletons[key] = instance
    }
    
    // MARK: - Resolution
    
    /// Resolve a service by type
    func resolve<T>(_ type: T.Type) -> T {
        let key = String(describing: type)
        
        // Check if we have a singleton instance
        if let singleton = singletons[key] as? T {
            return singleton
        }
        
        // Check if we have a factory
        guard let factory = factories[key] else {
            fatalError("Service \(type) not registered")
        }
        
        let instance = factory() as! T
        
        // Store as singleton if it was registered as singleton
        if factories[key] != nil {
            singletons[key] = instance
        }
        
        return instance
    }
    
    /// Optional resolve - returns nil if not registered
    func resolveOptional<T>(_ type: T.Type) -> T? {
        let key = String(describing: type)
        
        if let singleton = singletons[key] as? T {
            return singleton
        }
        
        guard let factory = factories[key] else {
            return nil
        }
        
        let instance = factory() as! T
        singletons[key] = instance
        return instance
    }
    
    // MARK: - Cleanup
    
    /// Clear all registrations (useful for testing)
    func clear() {
        services.removeAll()
        factories.removeAll()
        singletons.removeAll()
    }
    
    /// Remove specific service
    func remove<T>(_ type: T.Type) {
        let key = String(describing: type)
        services.removeValue(forKey: key)
        factories.removeValue(forKey: key)
        singletons.removeValue(forKey: key)
    }
}

// MARK: - Service Registration Extension

extension DIContainer {
    /// Register all core services
    func registerCoreServices() {
        // Core Services
        registerSingleton(LoggerServiceProtocol.self) {
            LoggerService()
        }

        registerSingleton(HapticServiceProtocol.self) {
            HapticService()
        }

        registerSingleton(AudioServiceProtocol.self) {
            AudioService()
        }

        // Data Services (these will be registered with actual instances in the app)
        // Repository registrations are handled in the app setup
        
        // ML Services
        registerSingleton(MLProcessingServiceProtocol.self) {
            MLProcessingService()
        }

        registerSingleton(FormAnalysisServiceProtocol.self) {
            FormAnalysisService()
        }

        // AR Services
        registerSingleton(ARTrackingServiceProtocol.self) {
            ARTrackingService()
        }

        registerSingleton(BodyPoseServiceProtocol.self) {
            BodyPoseService()
        }

        // Feedback Services
        registerSingleton(FeedbackServiceProtocol.self) {
            FeedbackService()
        }

        registerSingleton(CoachingServiceProtocol.self) {
            CoachingService()
        }

        // Analytics Services
        registerSingleton(AnalyticsServiceProtocol.self) {
            AnalyticsService()
        }

        // Security Services
        registerSingleton(SecurityServiceProtocol.self) {
            SecurityService()
        }

        registerSingleton(PrivacyServiceProtocol.self) {
            PrivacyService()
        }
    }
}

// MARK: - Property Wrapper for Dependency Injection

@propertyWrapper
struct Injected<T> {
    private let container: DIContainer
    
    var wrappedValue: T {
        container.resolve(T.self)
    }
    
    init(container: DIContainer = .shared) {
        self.container = container
    }
}

@propertyWrapper
struct OptionalInjected<T> {
    private let container: DIContainer
    
    var wrappedValue: T? {
        container.resolveOptional(T.self)
    }
    
    init(container: DIContainer = .shared) {
        self.container = container
    }
}

// MARK: - Service Locator Pattern (Alternative)

final class ServiceLocator {
    static let shared = ServiceLocator()
    private let container = DIContainer.shared
    
    private init() {}
    
    func get<T>(_ type: T.Type) -> T {
        return container.resolve(type)
    }
    
    func getOptional<T>(_ type: T.Type) -> T? {
        return container.resolveOptional(type)
    }
}

// MARK: - Environment Key for SwiftUI

struct DIContainerKey: EnvironmentKey {
    static let defaultValue = DIContainer.shared
}

extension EnvironmentValues {
    var diContainer: DIContainer {
        get { self[DIContainerKey.self] }
        set { self[DIContainerKey.self] = newValue }
    }
}

// MARK: - SwiftUI View Extension

import SwiftUI

extension View {
    func withDependencyInjection() -> some View {
        self.environmentObject(DIContainer.shared)
    }
}
